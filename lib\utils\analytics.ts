import { Project, User, ProjectStatus } from "@/lib/types";
import { ProjectAnalyticsData, EnhancedMonthlyData } from "@/lib/types/dashboard";
import { format, subMonths, subDays, isWithinInterval, startOfMonth, endOfMonth, differenceInDays } from "date-fns";


type TimeRange = "month" | "quarter" | "year";

/**
 * Generates analytics data from projects and users
 * @param projects List of projects
 * @param supervisors List of supervisor users
 * @param timeRange Time range for analytics (month, quarter, year)
 * @returns ProjectAnalyticsData object
 */
export function generateAnalyticsData(
  projects: Project[],
  supervisors: User[],
  timeRange: TimeRange = "month"
): ProjectAnalyticsData {
  // Get date range based on selected time range
  const now = new Date();
  let startDate: Date;
  
  switch (timeRange) {
    case "month":
      startDate = subMonths(now, 1);
      break;
    case "quarter":
      startDate = subMonths(now, 3);
      break;
    case "year":
      startDate = subMonths(now, 12);
      break;
    default:
      startDate = subMonths(now, 1);
  }

  // Filter projects within the selected time range
  const projectsInRange = projects.filter(project => {
    const createdAt = project.createdAt ? new Date(project.createdAt) : new Date();
    return createdAt >= startDate;
  });

  // Calculate total projects
  const totalProjects = projects.length;

  // Calculate projects by status
  const projectsByStatus = projects.reduce(
    (acc, project) => {
      const status = project.status as ProjectStatus;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    },
    {
      active: 0,
      completed: 0,
      archived: 0,
      suspended: 0,
      pending_supervisor: 0,
    }
  );

  // Calculate projects by month
  const projectsByMonth = getProjectsByMonth(projects, timeRange);

  // Calculate supervisor load
  const supervisorLoad = getSupervisorLoad(projects, supervisors);

  // Calculate average completion time (in days)
  const averageCompletionTime = calculateAverageCompletionTime(projects);

  // Calculate project trends
  const projectTrends = getProjectTrends(projects, timeRange);

  return {
    totalProjects,
    projectsByStatus,
    projectsByMonth,
    supervisorLoad,
    averageCompletionTime,
    projectTrends,
  };
}

/**
 * Enhanced function to calculate comprehensive project data per month
 */
function getProjectsByMonth(projects: Project[], timeRange: TimeRange): EnhancedMonthlyData[] {
  const now = new Date();
  let months: Date[] = [];

  // Determine how many months to include based on time range
  const monthsCount = timeRange === "month" ? 6 : timeRange === "quarter" ? 12 : 24;

  // Generate array of months
  for (let i = 0; i < monthsCount; i++) {
    months.push(subMonths(now, i));
  }

  // Reverse to get chronological order
  months = months.reverse();

  // Calculate comprehensive data per month
  return months.map((monthDate, index) => {
    const monthStart = startOfMonth(monthDate);
    const monthEnd = endOfMonth(monthDate);
    const monthLabel = format(monthDate, "MMM yyyy");
    const shortLabel = format(monthDate, "MMM");

    // Projects created in this month
    const createdProjects = projects.filter(project => {
      const createdAt = project.createdAt ? new Date(project.createdAt) : null;
      if (!createdAt) return false;

      return isWithinInterval(createdAt, {
        start: monthStart,
        end: monthEnd
      });
    });

    // Projects completed in this month
    const completedProjects = projects.filter(project => {
      if (project.status !== "completed") return false;
      const lastActivity = new Date(project.lastActivity);

      return isWithinInterval(lastActivity, {
        start: monthStart,
        end: monthEnd
      });
    });

    // Projects active during this month (created before or during, not completed before)
    const activeProjects = projects.filter(project => {
      const createdAt = project.createdAt ? new Date(project.createdAt) : null;
      if (!createdAt || createdAt > monthEnd) return false;

      if (project.status === "completed") {
        const lastActivity = new Date(project.lastActivity);
        return lastActivity >= monthStart;
      }

      return true;
    });

    // Calculate growth rate (compared to previous month)
    const previousMonthData = index > 0 ? months[index - 1] : null;
    let growthRate = 0;

    if (previousMonthData && index > 0) {
      const prevMonthStart = startOfMonth(previousMonthData);
      const prevMonthEnd = endOfMonth(previousMonthData);

      const prevCreatedCount = projects.filter(project => {
        const createdAt = project.createdAt ? new Date(project.createdAt) : null;
        if (!createdAt) return false;

        return isWithinInterval(createdAt, {
          start: prevMonthStart,
          end: prevMonthEnd
        });
      }).length;

      if (prevCreatedCount > 0) {
        growthRate = ((createdProjects.length - prevCreatedCount) / prevCreatedCount) * 100;
      } else if (createdProjects.length > 0) {
        growthRate = 100; // 100% growth from 0
      }
    }

    // Calculate completion rate for projects created in this month
    const completionRate = createdProjects.length > 0
      ? (createdProjects.filter(p => p.status === "completed").length / createdProjects.length) * 100
      : 0;

    // Status breakdown for created projects
    const statusBreakdown = createdProjects.reduce((acc, project) => {
      const status = project.status as ProjectStatus;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {
      active: 0,
      completed: 0,
      archived: 0,
      suspended: 0,
      pending_supervisor: 0,
    });

    return {
      month: monthLabel,
      shortMonth: shortLabel,
      count: createdProjects.length,
      completed: completedProjects.length,
      active: activeProjects.length,
      growthRate: Math.round(growthRate * 10) / 10, // Round to 1 decimal
      completionRate: Math.round(completionRate * 10) / 10,
      statusBreakdown,
      monthStart,
      monthEnd,
      projects: createdProjects.map(p => ({
        id: p.id,
        title: p.title,
        status: p.status,
        createdAt: p.createdAt
      }))
    };
  });
}



/**
 * Calculates the number of projects per supervisor
 */
function getSupervisorLoad(projects: Project[], supervisors: User[]): { supervisorId: string; supervisorName: string; projectCount: number }[] {
  // Create a map of supervisor IDs to names
  const supervisorMap = supervisors.reduce((acc, supervisor) => {
    acc[supervisor.id] = supervisor.name;
    return acc;
  }, {} as Record<string, string>);
  
  // Count projects per supervisor
  const supervisorCounts: Record<string, number> = {};
  
  projects.forEach(project => {
    project.supervisorIds.forEach(supervisorId => {
      supervisorCounts[supervisorId] = (supervisorCounts[supervisorId] || 0) + 1;
    });
  });
  
  // Convert to array format
  return Object.entries(supervisorCounts)
    .map(([supervisorId, projectCount]) => ({
      supervisorId,
      supervisorName: supervisorMap[supervisorId] || "Unknown Supervisor",
      projectCount
    }))
    .sort((a, b) => b.projectCount - a.projectCount);
}

/**
 * Calculates the average time to complete a project (in days)
 */
function calculateAverageCompletionTime(projects: Project[]): number {
  const completedProjects = projects.filter(project => project.status === "completed");
  
  if (completedProjects.length === 0) {
    return 0;
  }
  
  const totalDays = completedProjects.reduce((total, project) => {
    const createdAt = project.createdAt ? new Date(project.createdAt) : new Date();
    const lastActivity = new Date(project.lastActivity);
    
    return total + differenceInDays(lastActivity, createdAt);
  }, 0);
  
  return Math.round(totalDays / completedProjects.length);
}

/**
 * Calculates project trends (new vs completed projects)
 */
function getProjectTrends(projects: Project[], timeRange: TimeRange): { period: string; newProjects: number; completedProjects: number }[] {
  const now = new Date();
  let periods: { start: Date; end: Date; label: string }[] = [];
  
  // Determine periods based on time range
  if (timeRange === "month") {
    // Last 4 weeks
    for (let i = 0; i < 4; i++) {
      const end = subDays(now, i * 7);
      const start = subDays(end, 6);
      periods.push({
        start,
        end,
        label: `Week ${4 - i}`
      });
    }
  } else if (timeRange === "quarter") {
    // Last 3 months
    for (let i = 0; i < 3; i++) {
      const end = subMonths(now, i);
      const start = startOfMonth(end);
      periods.push({
        start,
        end: endOfMonth(end),
        label: format(end, "MMM")
      });
    }
  } else {
    // Last 6 months for year view
    for (let i = 0; i < 6; i++) {
      const end = subMonths(now, i);
      const start = startOfMonth(end);
      periods.push({
        start,
        end: endOfMonth(end),
        label: format(end, "MMM")
      });
    }
  }
  
  // Reverse to get chronological order
  periods = periods.reverse();
  
  // Calculate new and completed projects for each period
  return periods.map(period => {
    const newProjects = projects.filter(project => {
      const createdAt = project.createdAt ? new Date(project.createdAt) : null;
      if (!createdAt) return false;
      
      return isWithinInterval(createdAt, {
        start: period.start,
        end: period.end
      });
    }).length;
    
    const completedProjects = projects.filter(project => {
      if (project.status !== "completed") return false;
      
      const lastActivity = new Date(project.lastActivity);
      return isWithinInterval(lastActivity, {
        start: period.start,
        end: period.end
      });
    }).length;
    
    return {
      period: period.label,
      newProjects,
      completedProjects
    };
  });
}

/**
 * Formats a status string to be more readable
 */
export function formatStatus(status: string): string {
  return status
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
