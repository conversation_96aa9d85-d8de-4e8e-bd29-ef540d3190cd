"use client";

import React, { useState, useMemo } from 'react';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  ComposedChart,
  Line,
  LineChart
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  LineChart as LineChartIcon,
  PieChart,
  Calendar,
  Target,
  Activity
} from 'lucide-react';
import { EnhancedMonthlyData } from '@/lib/types/dashboard';
import { cn } from '@/lib/utils';

interface EnhancedProjectsByMonthProps {
  data: EnhancedMonthlyData[];
  className?: string;
}

export function EnhancedProjectsByMonth({ data, className }: EnhancedProjectsByMonthProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'trends' | 'breakdown'>('overview');
  const [selectedMonth, setSelectedMonth] = useState<EnhancedMonthlyData | null>(null);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (data.length === 0) return null;

    const totalCreated = data.reduce((sum, month) => sum + month.count, 0);
    const totalCompleted = data.reduce((sum, month) => sum + month.completed, 0);
    const avgGrowthRate = data.reduce((sum, month) => sum + month.growthRate, 0) / data.length;
    const avgCompletionRate = data.reduce((sum, month) => sum + month.completionRate, 0) / data.length;
    
    // Find best and worst performing months
    const bestMonth = data.reduce((best, month) => 
      month.count > best.count ? month : best, data[0]);
    const worstMonth = data.reduce((worst, month) => 
      month.count < worst.count ? month : worst, data[0]);

    // Calculate trend direction
    const recentMonths = data.slice(-3);
    const earlierMonths = data.slice(-6, -3);
    const recentAvg = recentMonths.reduce((sum, m) => sum + m.count, 0) / recentMonths.length;
    const earlierAvg = earlierMonths.reduce((sum, m) => sum + m.count, 0) / earlierMonths.length;
    const trendDirection = recentAvg > earlierAvg ? 'up' : 'down';

    return {
      totalCreated,
      totalCompleted,
      avgGrowthRate: Math.round(avgGrowthRate * 10) / 10,
      avgCompletionRate: Math.round(avgCompletionRate * 10) / 10,
      bestMonth,
      worstMonth,
      trendDirection,
      monthlyAverage: Math.round(totalCreated / data.length * 10) / 10
    };
  }, [data]);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const monthData = data.find(d => d.shortMonth === label || d.month === label);
      
      return (
        <div className="dashboard-card p-4 shadow-lg border-0 min-w-[250px]">
          <h4 className="font-semibold text-card-foreground mb-2">{monthData?.month}</h4>
          <div className="space-y-1 text-sm">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-muted-foreground">{entry.name}:</span>
                </div>
                <span className="font-medium text-card-foreground">{entry.value}</span>
              </div>
            ))}
            {monthData && (
              <>
                <div className="border-t border-border pt-2 mt-2">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Growth Rate:</span>
                    <div className="flex items-center gap-1">
                      {monthData.growthRate > 0 ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                      <span className={cn(
                        "font-medium",
                        monthData.growthRate > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {monthData.growthRate > 0 ? '+' : ''}{monthData.growthRate}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Completion Rate:</span>
                    <span className="font-medium text-card-foreground">{monthData.completionRate}%</span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  // Handle chart click to show month details
  const handleChartClick = (data: any) => {
    if (data && data.activeLabel) {
      const monthData = data.find((d: EnhancedMonthlyData) => 
        d.shortMonth === data.activeLabel || d.month === data.activeLabel
      );
      setSelectedMonth(monthData || null);
    }
  };

  if (!data || data.length === 0) {
    return (
      <div className={cn("dashboard-card", className)}>
        <div className="dashboard-card-header">
          <h3 className="dashboard-section-title">Projects by Month</h3>
          <p className="dashboard-subtitle">No project data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("dashboard-card", className)}>
      <div className="dashboard-card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="dashboard-section-title flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Projects by Month
              {summaryStats && (
                <Badge variant="outline" className="ml-2 text-xs font-normal">
                  {summaryStats.totalCreated} Total
                </Badge>
              )}
            </h3>
            <p className="dashboard-subtitle">Comprehensive project analytics and trends</p>
          </div>
          
          {summaryStats && (
            <div className="flex items-center gap-2">
              <div className="text-right">
                <div className="text-sm font-medium text-card-foreground">
                  {summaryStats.monthlyAverage} avg/month
                </div>
                <div className="flex items-center gap-1 text-xs">
                  {summaryStats.trendDirection === 'up' ? (
                    <TrendingUp className="h-3 w-3 text-green-600" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-600" />
                  )}
                  <span className={cn(
                    summaryStats.trendDirection === 'up' ? "text-green-600" : "text-red-600"
                  )}>
                    {summaryStats.trendDirection === 'up' ? 'Trending up' : 'Trending down'}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="dashboard-card-content">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-muted rounded-lg p-1">
            <TabsTrigger value="overview" className="flex items-center gap-2 rounded-md">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="trends" className="flex items-center gap-2 rounded-md">
              <LineChartIcon className="h-4 w-4" />
              Trends
            </TabsTrigger>
            <TabsTrigger value="breakdown" className="flex items-center gap-2 rounded-md">
              <PieChart className="h-4 w-4" />
              Breakdown
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="h-[350px] mb-6">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={data}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  onClick={handleChartClick}
                >
                  <defs>
                    <linearGradient id="createdGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="completedGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis 
                    dataKey="shortMonth" 
                    className="text-xs"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis 
                    className="text-xs"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="count"
                    name="Created"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                    fill="url(#createdGradient)"
                    activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="completed"
                    name="Completed"
                    stroke="#10b981"
                    strokeWidth={2}
                    fill="url(#completedGradient)"
                    activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </div>

            {/* Summary Statistics */}
            {summaryStats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className="text-2xl font-bold text-card-foreground">{summaryStats.totalCreated}</div>
                  <div className="text-muted-foreground">Total Created</div>
                </div>
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className="text-2xl font-bold text-green-600">{summaryStats.totalCompleted}</div>
                  <div className="text-muted-foreground">Total Completed</div>
                </div>
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className="text-2xl font-bold text-card-foreground">{summaryStats.avgCompletionRate}%</div>
                  <div className="text-muted-foreground">Avg Completion</div>
                </div>
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className={cn(
                    "text-2xl font-bold",
                    summaryStats.avgGrowthRate > 0 ? "text-green-600" : "text-red-600"
                  )}>
                    {summaryStats.avgGrowthRate > 0 ? '+' : ''}{summaryStats.avgGrowthRate}%
                  </div>
                  <div className="text-muted-foreground">Avg Growth</div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="trends" className="mt-6">
            <div className="h-[350px] mb-6">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis
                    dataKey="shortMonth"
                    className="text-xs"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis
                    className="text-xs"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="growthRate"
                    name="Growth Rate (%)"
                    stroke="#f59e0b"
                    strokeWidth={3}
                    dot={{ r: 4, strokeWidth: 2, fill: '#fff', stroke: '#f59e0b' }}
                    activeDot={{ r: 6, strokeWidth: 2, stroke: '#fff', fill: '#f59e0b' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="completionRate"
                    name="Completion Rate (%)"
                    stroke="#8b5cf6"
                    strokeWidth={3}
                    dot={{ r: 4, strokeWidth: 2, fill: '#fff', stroke: '#8b5cf6' }}
                    activeDot={{ r: 6, strokeWidth: 2, stroke: '#fff', fill: '#8b5cf6' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Trend Analysis */}
            {summaryStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 rounded-lg bg-muted/50">
                  <h4 className="font-semibold text-card-foreground mb-2 flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Performance Highlights
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Best Month:</span>
                      <span className="font-medium text-card-foreground">
                        {summaryStats.bestMonth.shortMonth} ({summaryStats.bestMonth.count} projects)
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Lowest Month:</span>
                      <span className="font-medium text-card-foreground">
                        {summaryStats.worstMonth.shortMonth} ({summaryStats.worstMonth.count} projects)
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Avg Growth:</span>
                      <span className={cn(
                        "font-medium",
                        summaryStats.avgGrowthRate > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {summaryStats.avgGrowthRate > 0 ? '+' : ''}{summaryStats.avgGrowthRate}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-muted/50">
                  <h4 className="font-semibold text-card-foreground mb-2 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Recent Trends
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Direction:</span>
                      <div className="flex items-center gap-1">
                        {summaryStats.trendDirection === 'up' ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                        <span className={cn(
                          "font-medium",
                          summaryStats.trendDirection === 'up' ? "text-green-600" : "text-red-600"
                        )}>
                          {summaryStats.trendDirection === 'up' ? 'Upward' : 'Downward'}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Monthly Avg:</span>
                      <span className="font-medium text-card-foreground">{summaryStats.monthlyAverage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Completion Rate:</span>
                      <span className="font-medium text-card-foreground">{summaryStats.avgCompletionRate}%</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="breakdown" className="mt-6">
            <div className="h-[350px] mb-6">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={data}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis
                    dataKey="shortMonth"
                    className="text-xs"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis
                    className="text-xs"
                    tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    dataKey="count"
                    name="Created"
                    fill="hsl(var(--primary))"
                    radius={[4, 4, 0, 0]}
                    opacity={0.8}
                  />
                  <Bar
                    dataKey="completed"
                    name="Completed"
                    fill="#10b981"
                    radius={[4, 4, 0, 0]}
                    opacity={0.8}
                  />
                  <Bar
                    dataKey="active"
                    name="Active"
                    fill="#f59e0b"
                    radius={[4, 4, 0, 0]}
                    opacity={0.8}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Monthly Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.slice(-6).map((month) => (
                <div
                  key={month.month}
                  className="p-4 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors cursor-pointer"
                  onClick={() => setSelectedMonth(month)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h5 className="font-semibold text-card-foreground">{month.shortMonth}</h5>
                    <div className="flex items-center gap-1">
                      {month.growthRate > 0 ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                      <span className={cn(
                        "text-xs font-medium",
                        month.growthRate > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {month.growthRate > 0 ? '+' : ''}{month.growthRate}%
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Created:</span>
                      <span className="font-medium text-card-foreground">{month.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Completed:</span>
                      <span className="font-medium text-green-600">{month.completed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Active:</span>
                      <span className="font-medium text-amber-600">{month.active}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Completion:</span>
                      <span className="font-medium text-card-foreground">{month.completionRate}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Selected Month Details Modal/Panel */}
        {selectedMonth && (
          <div className="mt-6 p-4 rounded-lg border border-border bg-muted/30">
            <div className="flex justify-between items-start mb-4">
              <h4 className="font-semibold text-card-foreground">{selectedMonth.month} Details</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedMonth(null)}
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Projects Created</div>
                <div className="text-xl font-bold text-card-foreground">{selectedMonth.count}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Projects Completed</div>
                <div className="text-xl font-bold text-green-600">{selectedMonth.completed}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Growth Rate</div>
                <div className={cn(
                  "text-xl font-bold",
                  selectedMonth.growthRate > 0 ? "text-green-600" : "text-red-600"
                )}>
                  {selectedMonth.growthRate > 0 ? '+' : ''}{selectedMonth.growthRate}%
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Completion Rate</div>
                <div className="text-xl font-bold text-card-foreground">{selectedMonth.completionRate}%</div>
              </div>
            </div>

            {selectedMonth.projects.length > 0 && (
              <div className="mt-4">
                <h5 className="font-medium text-card-foreground mb-2">Projects Created ({selectedMonth.projects.length})</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {selectedMonth.projects.map((project) => (
                    <div key={project.id} className="text-sm p-2 rounded bg-background/50">
                      <div className="font-medium text-card-foreground truncate">{project.title}</div>
                      <div className="text-muted-foreground capitalize">{project.status}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
